# FastAPI EventStream 流式数据API

这个项目提供了多种EventStream流式数据传输的API接口，包括文本流、Server-Sent Events (SSE)、音频流等。

## 功能特性

- ✅ 文本流式传输
- ✅ Server-Sent Events (SSE) 格式流式传输
- ✅ 音频文件流式传输
- ✅ 带元数据的音频块流式传输
- ✅ CORS支持
- ✅ 实时进度反馈

## 安装依赖

```bash
pip install fastapi uvicorn
```

## 启动服务

```bash
python main2.py
```

服务将在 `http://127.0.0.1:8000` 启动

## API接口

### 1. 基础接口

**GET /** - 欢迎页面
```
http://127.0.0.1:8000/
```

### 2. 文本流式传输

**GET /stream/text** - 简单文本流
```
http://127.0.0.1:8000/stream/text
```

返回格式：
```
data: {"id": 1, "message": "这是第一条消息", "timestamp": 1754020446.476023}

data: {"id": 2, "message": "这是第二条消息", "timestamp": 1754020447.477618}
```

### 3. Server-Sent Events (SSE)

**GET /stream/events** - SSE格式流式传输
```
http://127.0.0.1:8000/stream/events
```

返回格式：
```
event: message
id: 1
data: {"event_id": 1, "message": "这是第 1 个事件", "timestamp": 1754020446.476023, "progress": 10}

event: end
data: {"message": "流式传输结束"}
```

### 4. 音频流式传输

**GET /stream/audio** - 直接音频流
```
http://127.0.0.1:8000/stream/audio
```

- 直接返回音频文件的二进制流
- 支持音频播放器直接播放
- 使用 `music.mp3` 文件

### 5. 音频块流式传输

**GET /stream/audio-chunks** - 带元数据的音频块流
```
http://127.0.0.1:8000/stream/audio-chunks
```

返回格式：
```
event: metadata
data: {"chunk_number": 1, "chunk_size": 8192, "timestamp": 1754020446.476023}

event: audio
data: <base64编码的音频数据>

event: end
data: {"message": "音频传输完成"}
```

## 测试页面

打开 `test_stream.html` 文件可以进行可视化测试：

```
file:///c:/Users/<USER>/Desktop/1234/test_stream.html
```

测试页面包含：
- 文本流测试
- SSE事件流测试（带进度条）
- 音频流播放测试
- 音频块流测试

## 使用示例

### JavaScript客户端示例

#### 1. 文本流
```javascript
async function fetchTextStream() {
    const response = await fetch('http://127.0.0.1:8000/stream/text');
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        console.log(chunk);
    }
}
```

#### 2. SSE事件流
```javascript
const eventSource = new EventSource('http://127.0.0.1:8000/stream/events');

eventSource.addEventListener('message', function(event) {
    const data = JSON.parse(event.data);
    console.log('消息:', data.message, '进度:', data.progress + '%');
});

eventSource.addEventListener('end', function(event) {
    console.log('流式传输结束');
    eventSource.close();
});
```

#### 3. 音频流
```javascript
const audio = new Audio('http://127.0.0.1:8000/stream/audio');
audio.play();
```

### Python客户端示例

```python
import requests

# 文本流
response = requests.get('http://127.0.0.1:8000/stream/text', stream=True)
for chunk in response.iter_content(chunk_size=1024):
    if chunk:
        print(chunk.decode('utf-8'))

# SSE事件流
import sseclient

response = requests.get('http://127.0.0.1:8000/stream/events', stream=True)
client = sseclient.SSEClient(response)
for event in client.events():
    print(f'事件类型: {event.event}, 数据: {event.data}')
```

### cURL示例

```bash
# 文本流
curl -N http://127.0.0.1:8000/stream/text

# SSE事件流
curl -N -H "Accept: text/event-stream" http://127.0.0.1:8000/stream/events

# 下载音频流
curl -o downloaded_music.mp3 http://127.0.0.1:8000/stream/audio
```

## 技术特点

1. **异步处理**: 使用FastAPI的异步特性，支持高并发
2. **流式传输**: 数据分块传输，减少内存占用
3. **实时性**: 支持实时数据推送
4. **跨域支持**: 配置了CORS中间件
5. **多种格式**: 支持文本、JSON、二进制等多种数据格式

## 注意事项

1. 确保 `music.mp3` 文件存在于项目根目录
2. 音频流接口会检查文件是否存在
3. SSE连接需要客户端正确处理事件类型
4. 流式传输过程中避免频繁的网络中断

## 扩展建议

- 添加身份验证
- 支持更多音频格式
- 添加流量控制
- 实现断点续传
- 添加日志记录
