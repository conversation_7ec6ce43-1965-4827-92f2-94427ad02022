import requests
import json

def generate_wxacode(access_token, path, width=430, auto_color=False, line_color=None, is_hyaline=False):
    """
    生成微信小程序码（永久有效，数量不限）
    
    参数:
    - access_token: 微信接口调用凭证
    - path: 扫码进入的小程序页面路径，最大长度128字节
    - width: 二维码的宽度，单位px，默认430
    - auto_color: 是否自动配置线条颜色，默认False
    - line_color: auto_color为False时生效，使用rgb设置颜色，如{"r":"0","g":"0","b":"0"}
    - is_hyaline: 是否需要透明底色，默认False
    
    返回:
    - 成功返回图片二进制数据，失败返回None
    """
    url = "https://api.weixin.qq.com/wxa/getwxacode?access_token={}".format(access_token)
    
    data = {
        "path": path,
        "width": width,
        "auto_color": auto_color,
        "is_hyaline": is_hyaline
    }
    
    if line_color and not auto_color:
        data["line_color"] = line_color
    
    response = requests.post(url, json=data)
    
    # 判断返回的是否是图片
    if response.headers.get('Content-Type') == 'image/jpeg':
        return response.content
    else:
        # 不是图片，可能是错误信息
        error_info = json.loads(response.text)
        print("生成小程序码失败:", error_info)
        return None

# 使用示例
if __name__ == "__main__":
    # 你需要先获取access_token，这里只是一个示例
    access_token = "94_WDucXmVy_KRmTZ2LAqKvw4dhS5bUJKaFGsMyQqTOqnyWJB84pXCCAWmYW7P1Yl2wlm72ssC147MsueyEzou-tDlUDeLUbOy35zCTQit7OXlSdcTvJGV5QmwuDrUQTPhAHAUXT"
    path = "pages/index/index?param1=value1&param2=value2"
    
    # 生成小程序码
    qr_code = generate_wxacode(access_token, path)
    
    if qr_code:
        # 保存到文件
        with open("wxacode.jpg", "wb") as f:
            f.write(qr_code)
        print("小程序码已生成并保存为wxacode.jpg")
    else:
        print("生成小程序码失败")

