<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音合成API测试 - 模拟mstf-kit</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .output {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            min-height: 100px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
        .audio-player {
            margin: 10px 0;
            width: 100%;
        }
    </style>
</head>
<body>
    <h1>语音合成API测试 - 模拟你的JavaScript代码</h1>
    
    <div class="section">
        <h2>语音合成流式传输测试</h2>
        
        <div class="form-group">
            <label>文本内容:</label>
            <textarea id="promptInput" rows="3" style="width: 500px;">你好，这是一个语音合成测试。我们正在测试流式音频传输功能。</textarea>
        </div>
        
        <div class="form-group">
            <label>音色ID:</label>
            <input type="number" id="cloneIdInput" value="1" style="width: 100px;">
        </div>
        
        <div class="form-group">
            <label>语速:</label>
            <input type="number" id="speedInput" value="1.0" step="0.1" min="0.5" max="2.0" style="width: 100px;">
        </div>
        
        <button onclick="startSynthesis()">开始语音合成</button>
        <button onclick="stopSynthesis()">停止合成</button>
        <button onclick="clearOutput()">清空日志</button>
        <button onclick="downloadAudio()" id="downloadBtn" disabled>下载音频</button>
        
        <div class="progress">
            <div id="progressBar" class="progress-bar"></div>
        </div>
        
        <audio id="audioPlayer" class="audio-player" controls></audio>
        
        <h3>实时日志:</h3>
        <div id="logOutput" class="output"></div>
    </div>

    <script>
        let currentReader = null;
        let audioChunks = [];
        let finalAudioBlob = null;
        
        function log(message) {
            const output = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('logOutput').textContent = '';
            document.getElementById('progressBar').style.width = '0%';
            audioChunks = [];
            finalAudioBlob = null;
            document.getElementById('downloadBtn').disabled = true;
        }
        
        function updateProgress(progress) {
            document.getElementById('progressBar').style.width = progress + '%';
        }
        
        // 模拟 processStreamResponse 函数的核心功能
        function processStreamResponse(reader, options = {}) {
            return new Promise((resolve, reject) => {
                const decoder = new TextDecoder();
                let buffer = '';
                
                function readChunk() {
                    reader.read().then(({ done, value }) => {
                        if (done) {
                            // 处理完成
                            if (audioChunks.length > 0) {
                                finalAudioBlob = new Blob(audioChunks, { type: 'audio/mpeg' });
                                log(`语音合成完成！总共接收 ${audioChunks.length} 个音频块，完整音频大小: ${finalAudioBlob.size} bytes`);
                                
                                // 设置音频播放器
                                const audioUrl = URL.createObjectURL(finalAudioBlob);
                                document.getElementById('audioPlayer').src = audioUrl;
                                document.getElementById('downloadBtn').disabled = false;
                                
                                if (options.onComplete) {
                                    options.onComplete(finalAudioBlob);
                                }
                                
                                resolve({
                                    success: true,
                                    chunksReceived: audioChunks.length,
                                    totalSize: finalAudioBlob.size,
                                    audioBlob: finalAudioBlob
                                });
                            } else {
                                reject(new Error('未收到音频数据'));
                            }
                            return;
                        }
                        
                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop(); // 保留不完整的行
                        
                        for (const line of lines) {
                            if (line.startsWith('event: ')) {
                                const eventType = line.substring(7);
                                continue;
                            }
                            
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.substring(6));
                                    
                                    if (data.message) {
                                        log(`事件: ${data.message}`);
                                        if (data.total_size) {
                                            log(`预计音频大小: ${data.total_size} 字节`);
                                        }
                                    } else if (data.chunk_number) {
                                        log(`接收元数据 - 块 ${data.chunk_number}: ${data.chunk_size} 字节 (${data.progress}%)`);
                                        updateProgress(data.progress);
                                    }
                                } catch (e) {
                                    // 可能是base64音频数据
                                    if (line.length > 100) {
                                        try {
                                            const base64Data = line.substring(6);
                                            const binaryString = atob(base64Data);
                                            const bytes = new Uint8Array(binaryString.length);
                                            for (let i = 0; i < binaryString.length; i++) {
                                                bytes[i] = binaryString.charCodeAt(i);
                                            }
                                            audioChunks.push(bytes);
                                            log(`接收音频数据块 ${audioChunks.length}: ${bytes.length} 字节`);
                                            
                                            if (options.onAudioData) {
                                                const blob = new Blob([bytes], { type: 'audio/mpeg' });
                                                options.onAudioData(blob);
                                            }
                                        } catch (decodeError) {
                                            log(`解码音频数据失败: ${decodeError.message}`);
                                        }
                                    }
                                }
                            }
                        }
                        
                        readChunk(); // 继续读取下一块
                    }).catch(error => {
                        log(`读取流数据错误: ${error.message}`);
                        if (options.onError) {
                            options.onError(error);
                        }
                        reject(error);
                    });
                }
                
                readChunk(); // 开始读取
            });
        }
        
        // 模拟你的 synthesize 函数
        async function startSynthesis() {
            if (currentReader) {
                log('已有合成任务在进行中，请先停止');
                return;
            }
            
            const prompt = document.getElementById('promptInput').value.trim();
            const cloneId = parseInt(document.getElementById('cloneIdInput').value);
            const speed = parseFloat(document.getElementById('speedInput').value);
            
            if (!prompt) {
                log('错误: 请输入文本内容');
                return;
            }
            
            clearOutput();
            log('开始语音合成...');
            log(`参数: prompt="${prompt}", clone_id=${cloneId}, speed=${speed}`);
            
            try {
                // 模拟你的API调用
                const response = await fetch('http://127.0.0.1:8000/stream/audio-chunks', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        clone_id: cloneId,
                        speed: speed
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                currentReader = response.body.getReader();
                log('开始流式语音合成，reader已获取');
                
                // 使用 processStreamResponse 处理音频流
                const result = await processStreamResponse(currentReader, {
                    returnBlob: true,
                    onAudioData: async (blob) => {
                        log(`收到音频数据块，大小: ${blob.size} bytes`);
                        // 这里可以实现实时播放逻辑
                    },
                    onComplete: (finalBlob) => {
                        log('语音合成流式传输完成！');
                        currentReader = null;
                    },
                    onError: (error) => {
                        log(`processStreamResponse 错误: ${error.message}`);
                        currentReader = null;
                    }
                });
                
                log(`合成结果: 成功=${result.success}, 块数=${result.chunksReceived}, 总大小=${result.totalSize}`);
                
            } catch (error) {
                log(`流式语音合成错误: ${error.message}`);
                currentReader = null;
            }
        }
        
        function stopSynthesis() {
            if (currentReader) {
                currentReader.cancel();
                currentReader = null;
                log('语音合成已停止');
            }
        }
        
        function downloadAudio() {
            if (finalAudioBlob) {
                const url = URL.createObjectURL(finalAudioBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'synthesized_audio.mp3';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                log('音频下载已开始');
            }
        }
    </script>
</body>
</html>
