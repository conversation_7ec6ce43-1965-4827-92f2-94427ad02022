<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EventStream 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .output {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            min-height: 100px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>FastAPI EventStream 测试页面</h1>
    
    <div class="section">
        <h2>1. 文本流式传输测试</h2>
        <button onclick="testTextStream()">开始文本流</button>
        <button onclick="clearOutput('textOutput')">清空</button>
        <div id="textOutput" class="output"></div>
    </div>
    
    <div class="section">
        <h2>2. Server-Sent Events 测试</h2>
        <button onclick="testSSE()">开始SSE流</button>
        <button onclick="stopSSE()">停止SSE</button>
        <button onclick="clearOutput('sseOutput')">清空</button>
        <div class="progress">
            <div id="progressBar" class="progress-bar"></div>
        </div>
        <div id="sseOutput" class="output"></div>
    </div>
    
    <div class="section">
        <h2>3. 音频流式传输测试</h2>
        <button onclick="testAudioStream()">播放音频流</button>
        <button onclick="stopAudio()">停止播放</button>
        <audio id="audioPlayer" controls style="width: 100%; margin: 10px 0;"></audio>
        <div id="audioOutput" class="output"></div>
    </div>
    
    <div class="section">
        <h2>4. 音频块流式传输测试</h2>
        <button onclick="testAudioChunks()">开始音频块流</button>
        <button onclick="stopAudioChunks()">停止</button>
        <button onclick="clearOutput('audioChunksOutput')">清空</button>
        <div id="audioChunksOutput" class="output"></div>
    </div>

    <script>
        let sseEventSource = null;
        let audioChunksEventSource = null;
        
        function clearOutput(elementId) {
            document.getElementById(elementId).textContent = '';
        }
        
        function appendOutput(elementId, text) {
            const output = document.getElementById(elementId);
            output.textContent += text + '\n';
            output.scrollTop = output.scrollHeight;
        }
        
        // 文本流测试
        async function testTextStream() {
            try {
                const response = await fetch('http://127.0.0.1:8000/stream/text');
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                clearOutput('textOutput');
                appendOutput('textOutput', '开始接收文本流...');
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    appendOutput('textOutput', chunk);
                }
                
                appendOutput('textOutput', '文本流接收完成');
            } catch (error) {
                appendOutput('textOutput', '错误: ' + error.message);
            }
        }
        
        // SSE测试
        function testSSE() {
            if (sseEventSource) {
                sseEventSource.close();
            }
            
            clearOutput('sseOutput');
            document.getElementById('progressBar').style.width = '0%';
            
            sseEventSource = new EventSource('http://127.0.0.1:8000/stream/events');
            
            sseEventSource.onopen = function(event) {
                appendOutput('sseOutput', 'SSE连接已建立');
            };
            
            sseEventSource.addEventListener('message', function(event) {
                const data = JSON.parse(event.data);
                appendOutput('sseOutput', `消息: ${data.message} (进度: ${data.progress}%)`);
                document.getElementById('progressBar').style.width = data.progress + '%';
            });
            
            sseEventSource.addEventListener('end', function(event) {
                const data = JSON.parse(event.data);
                appendOutput('sseOutput', `结束: ${data.message}`);
                sseEventSource.close();
                sseEventSource = null;
            });
            
            sseEventSource.onerror = function(event) {
                appendOutput('sseOutput', 'SSE连接错误');
                sseEventSource.close();
                sseEventSource = null;
            };
        }
        
        function stopSSE() {
            if (sseEventSource) {
                sseEventSource.close();
                sseEventSource = null;
                appendOutput('sseOutput', 'SSE连接已关闭');
            }
        }
        
        // 音频流测试
        function testAudioStream() {
            const audio = document.getElementById('audioPlayer');
            audio.src = 'http://127.0.0.1:8000/stream/audio';
            audio.load();
            audio.play().catch(error => {
                appendOutput('audioOutput', '音频播放错误: ' + error.message);
            });
            appendOutput('audioOutput', '开始播放音频流...');
        }
        
        function stopAudio() {
            const audio = document.getElementById('audioPlayer');
            audio.pause();
            audio.currentTime = 0;
            appendOutput('audioOutput', '音频播放已停止');
        }
        
        // 音频块流测试
        function testAudioChunks() {
            if (audioChunksEventSource) {
                audioChunksEventSource.close();
            }
            
            clearOutput('audioChunksOutput');
            
            audioChunksEventSource = new EventSource('http://127.0.0.1:8000/stream/audio-chunks');
            
            audioChunksEventSource.onopen = function(event) {
                appendOutput('audioChunksOutput', '音频块流连接已建立');
            };
            
            audioChunksEventSource.addEventListener('metadata', function(event) {
                const data = JSON.parse(event.data);
                appendOutput('audioChunksOutput', `块 ${data.chunk_number}: ${data.chunk_size} 字节`);
            });
            
            audioChunksEventSource.addEventListener('audio', function(event) {
                // 这里可以处理base64编码的音频数据
                // 为了演示，我们只显示接收到数据
                appendOutput('audioChunksOutput', '接收到音频数据块');
            });
            
            audioChunksEventSource.addEventListener('end', function(event) {
                const data = JSON.parse(event.data);
                appendOutput('audioChunksOutput', `结束: ${data.message}`);
                audioChunksEventSource.close();
                audioChunksEventSource = null;
            });
            
            audioChunksEventSource.onerror = function(event) {
                appendOutput('audioChunksOutput', '音频块流连接错误');
                audioChunksEventSource.close();
                audioChunksEventSource = null;
            };
        }
        
        function stopAudioChunks() {
            if (audioChunksEventSource) {
                audioChunksEventSource.close();
                audioChunksEventSource = null;
                appendOutput('audioChunksOutput', '音频块流连接已关闭');
            }
        }
    </script>
</body>
</html>
