<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EventStream 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .output {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            min-height: 100px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>FastAPI EventStream 测试页面</h1>
    
    <div class="section">
        <h2>1. 文本流式传输测试</h2>
        <button onclick="testTextStream()">开始文本流</button>
        <button onclick="clearOutput('textOutput')">清空</button>
        <div id="textOutput" class="output"></div>
    </div>
    
    <div class="section">
        <h2>2. Server-Sent Events 测试</h2>
        <button onclick="testSSE()">开始SSE流</button>
        <button onclick="stopSSE()">停止SSE</button>
        <button onclick="clearOutput('sseOutput')">清空</button>
        <div class="progress">
            <div id="progressBar" class="progress-bar"></div>
        </div>
        <div id="sseOutput" class="output"></div>
    </div>
    
    <div class="section">
        <h2>3. 音频流式传输测试</h2>
        <button onclick="testAudioStream()">播放音频流</button>
        <button onclick="stopAudio()">停止播放</button>
        <audio id="audioPlayer" controls style="width: 100%; margin: 10px 0;"></audio>
        <div id="audioOutput" class="output"></div>
    </div>
    
    <div class="section">
        <h2>4. 语音合成流式传输测试 (POST)</h2>
        <div style="margin: 10px 0;">
            <label>文本内容: </label>
            <input type="text" id="promptInput" value="你好，这是一个语音合成测试" style="width: 300px; padding: 5px;">
        </div>
        <div style="margin: 10px 0;">
            <label>音色ID: </label>
            <input type="number" id="cloneIdInput" value="1" style="width: 100px; padding: 5px;">
            <label style="margin-left: 20px;">语速: </label>
            <input type="number" id="speedInput" value="1.0" step="0.1" min="0.5" max="2.0" style="width: 100px; padding: 5px;">
        </div>
        <button onclick="testTextToSpeech()">开始语音合成</button>
        <button onclick="stopAudioChunks()">停止</button>
        <button onclick="clearOutput('audioChunksOutput')">清空</button>
        <div class="progress">
            <div id="ttsProgressBar" class="progress-bar"></div>
        </div>
        <div id="audioChunksOutput" class="output"></div>
    </div>

    <script>
        let sseEventSource = null;
        let audioChunksEventSource = null;
        
        function clearOutput(elementId) {
            document.getElementById(elementId).textContent = '';
        }
        
        function appendOutput(elementId, text) {
            const output = document.getElementById(elementId);
            output.textContent += text + '\n';
            output.scrollTop = output.scrollHeight;
        }
        
        // 文本流测试
        async function testTextStream() {
            try {
                const response = await fetch('http://127.0.0.1:8000/stream/text');
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                clearOutput('textOutput');
                appendOutput('textOutput', '开始接收文本流...');
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    appendOutput('textOutput', chunk);
                }
                
                appendOutput('textOutput', '文本流接收完成');
            } catch (error) {
                appendOutput('textOutput', '错误: ' + error.message);
            }
        }
        
        // SSE测试
        function testSSE() {
            if (sseEventSource) {
                sseEventSource.close();
            }
            
            clearOutput('sseOutput');
            document.getElementById('progressBar').style.width = '0%';
            
            sseEventSource = new EventSource('http://127.0.0.1:8000/stream/events');
            
            sseEventSource.onopen = function(event) {
                appendOutput('sseOutput', 'SSE连接已建立');
            };
            
            sseEventSource.addEventListener('message', function(event) {
                const data = JSON.parse(event.data);
                appendOutput('sseOutput', `消息: ${data.message} (进度: ${data.progress}%)`);
                document.getElementById('progressBar').style.width = data.progress + '%';
            });
            
            sseEventSource.addEventListener('end', function(event) {
                const data = JSON.parse(event.data);
                appendOutput('sseOutput', `结束: ${data.message}`);
                sseEventSource.close();
                sseEventSource = null;
            });
            
            sseEventSource.onerror = function(event) {
                appendOutput('sseOutput', 'SSE连接错误');
                sseEventSource.close();
                sseEventSource = null;
            };
        }
        
        function stopSSE() {
            if (sseEventSource) {
                sseEventSource.close();
                sseEventSource = null;
                appendOutput('sseOutput', 'SSE连接已关闭');
            }
        }
        
        // 音频流测试
        function testAudioStream() {
            const audio = document.getElementById('audioPlayer');
            audio.src = 'http://127.0.0.1:8000/stream/audio';
            audio.load();
            audio.play().catch(error => {
                appendOutput('audioOutput', '音频播放错误: ' + error.message);
            });
            appendOutput('audioOutput', '开始播放音频流...');
        }
        
        function stopAudio() {
            const audio = document.getElementById('audioPlayer');
            audio.pause();
            audio.currentTime = 0;
            appendOutput('audioOutput', '音频播放已停止');
        }
        
        // 语音合成流式传输测试
        async function testTextToSpeech() {
            if (audioChunksEventSource) {
                audioChunksEventSource.close();
            }

            clearOutput('audioChunksOutput');
            document.getElementById('ttsProgressBar').style.width = '0%';

            // 获取输入参数
            const prompt = document.getElementById('promptInput').value;
            const cloneId = parseInt(document.getElementById('cloneIdInput').value);
            const speed = parseFloat(document.getElementById('speedInput').value);

            if (!prompt.trim()) {
                appendOutput('audioChunksOutput', '错误: 请输入文本内容');
                return;
            }

            try {
                // 发送POST请求到语音合成接口
                const response = await fetch('http://127.0.0.1:8000/stream/audio-chunks', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        clone_id: cloneId,
                        speed: speed
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                appendOutput('audioChunksOutput', `开始语音合成: "${prompt}"`);
                appendOutput('audioChunksOutput', `参数: 音色ID=${cloneId}, 语速=${speed}`);

                let audioChunks = [];
                let chunksReceived = 0;

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('event: ')) {
                            const eventType = line.substring(7);
                            continue;
                        }

                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.substring(6));

                                if (line.includes('"message"')) {
                                    if (data.message === '开始语音合成') {
                                        appendOutput('audioChunksOutput', `开始合成，预计大小: ${data.total_size} 字节`);
                                    } else if (data.message === '语音合成完成') {
                                        appendOutput('audioChunksOutput', `合成完成！总块数: ${data.total_chunks}, 总大小: ${data.total_size} 字节`);
                                        document.getElementById('ttsProgressBar').style.width = '100%';
                                    } else if (data.message === '流式传输结束') {
                                        appendOutput('audioChunksOutput', '传输结束');
                                    }
                                } else if (data.chunk_number) {
                                    // 元数据
                                    chunksReceived++;
                                    const progress = data.progress || 0;
                                    document.getElementById('ttsProgressBar').style.width = progress + '%';
                                    appendOutput('audioChunksOutput', `块 ${data.chunk_number}: ${data.chunk_size} 字节 (${progress.toFixed(1)}%)`);
                                }
                            } catch (e) {
                                // 可能是base64音频数据，跳过解析
                                if (line.length > 100) {
                                    appendOutput('audioChunksOutput', `接收到音频数据块 (${line.length} 字符)`);
                                }
                            }
                        }
                    }
                }

                appendOutput('audioChunksOutput', `语音合成流式传输完成，共接收 ${chunksReceived} 个数据块`);

            } catch (error) {
                appendOutput('audioChunksOutput', '错误: ' + error.message);
                console.error('语音合成错误:', error);
            }
        }
        
        function stopAudioChunks() {
            if (audioChunksEventSource) {
                audioChunksEventSource.close();
                audioChunksEventSource = null;
                appendOutput('audioChunksOutput', '音频块流连接已关闭');
            }
        }
    </script>
</body>
</html>
