from fastapi import FastAP<PERSON>
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import os
import json
import time
from typing import Generator

app = FastAPI()

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get('/')
def home():
    return "欢迎使用fastAPI - 支持EventStream流式数据"

@app.get("/stream/text")
async def stream_text():
    """文本流式传输示例"""
    def generate_text():
        messages = [
            "这是第一条消息",
            "这是第二条消息",
            "这是第三条消息",
            "流式传输完成"
        ]

        for i, message in enumerate(messages):
            data = {
                "id": i + 1,
                "message": message,
                "timestamp": time.time()
            }
            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
            time.sleep(1)  # 模拟延迟

    return StreamingResponse(
        generate_text(),
        media_type="text/plain; charset=utf-8",
        headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
    )

@app.get("/stream/events")
async def stream_events():
    """Server-Sent Events (SSE) 格式的流式传输"""
    def generate_events():
        for i in range(10):
            event_data = {
                "event_id": i + 1,
                "message": f"这是第 {i + 1} 个事件",
                "timestamp": time.time(),
                "progress": (i + 1) * 10
            }

            # SSE 格式
            yield f"event: message\n"
            yield f"id: {i + 1}\n"
            yield f"data: {json.dumps(event_data, ensure_ascii=False)}\n\n"
            time.sleep(0.5)

        # 发送结束事件
        yield f"event: end\n"
        yield f"data: {json.dumps({'message': '流式传输结束'}, ensure_ascii=False)}\n\n"

    return StreamingResponse(
        generate_events(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )

@app.get("/stream/audio")
async def stream_audio():
    """音频文件流式传输"""
    audio_file = "music.mp3"

    if not os.path.exists(audio_file):
        return {"error": "音频文件不存在"}

    def generate_audio():
        with open(audio_file, "rb") as f:
            while True:
                chunk = f.read(8192)  # 每次读取8KB
                if not chunk:
                    break
                yield chunk

    file_size = os.path.getsize(audio_file)

    return StreamingResponse(
        generate_audio(),
        media_type="audio/mpeg",
        headers={
            "Content-Length": str(file_size),
            "Accept-Ranges": "bytes",
            "Cache-Control": "no-cache"
        }
    )

class TextToSpeechRequest(BaseModel):
    prompt: str
    clone_id: int
    speed: float = 1.0

@app.post("/stream/audio-chunks")
async def stream_audio_with_metadata(request: TextToSpeechRequest):
    """
    语音合成流式传输接口
    接收文本转语音请求，返回音频块流式数据
    """
    # 模拟语音合成过程，实际项目中这里应该调用真实的TTS服务
    print(f"收到语音合成请求: prompt='{request.prompt}', clone_id={request.clone_id}, speed={request.speed}")

    # 使用现有的音频文件作为示例输出
    audio_file = "music.mp3"

    if not os.path.exists(audio_file):
        return {"error": "音频文件不存在"}

    def generate_audio_chunks():
        import base64
        chunk_size = 8192  # 8KB per chunk
        chunk_number = 0
        total_size = os.path.getsize(audio_file)
        bytes_sent = 0

        # 发送开始事件
        yield f"event: start\n"
        yield f"data: {json.dumps({'message': '开始语音合成', 'total_size': total_size, 'prompt': request.prompt})}\n\n"

        with open(audio_file, "rb") as f:
            while True:
                chunk = f.read(chunk_size)
                if not chunk:
                    break

                chunk_number += 1
                bytes_sent += len(chunk)
                progress = (bytes_sent / total_size) * 100

                # 发送元数据事件
                metadata = {
                    "chunk_number": chunk_number,
                    "chunk_size": len(chunk),
                    "bytes_sent": bytes_sent,
                    "total_size": total_size,
                    "progress": round(progress, 2),
                    "timestamp": time.time()
                }

                yield f"event: metadata\n"
                yield f"data: {json.dumps(metadata)}\n\n"

                # 发送音频数据事件（base64编码）
                encoded_chunk = base64.b64encode(chunk).decode('utf-8')

                yield f"event: audio\n"
                yield f"data: {encoded_chunk}\n\n"

                # 控制传输速度，模拟实时语音合成
                time.sleep(0.05)  # 50ms延迟

        # 发送完成事件
        completion_data = {
            "message": "语音合成完成",
            "total_chunks": chunk_number,
            "total_size": bytes_sent,
            "prompt": request.prompt,
            "clone_id": request.clone_id,
            "speed": request.speed
        }

        yield f"event: complete\n"
        yield f"data: {json.dumps(completion_data)}\n\n"

        yield f"event: end\n"
        yield f"data: {json.dumps({'message': '流式传输结束'})}\n\n"

    return StreamingResponse(
        generate_audio_chunks(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type",
            "Access-Control-Allow-Methods": "POST, OPTIONS"
        }
    )

if __name__ == '__main__':
    uvicorn.run('main2:app', host="127.0.0.1", port=8000, reload=True)